{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-form": "^1.15.2", "@tanstack/react-query": "^5.84.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "hono": "^4.8.12", "lucide-react": "^0.487.0", "next": "15.3.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.1.1", "react-dom": "^19.1.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "zod": "^4.0.14"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query-devtools": "^5.84.0", "@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}}