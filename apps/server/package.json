{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsdown", "check-types": "tsc -b", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "bun run --hot src/index.ts", "start": "bun run dist/index.js", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev"}, "dependencies": {"@prisma/client": "^6.13.0", "bcryptjs": "^3.0.2", "dotenv": "^17.2.1", "hono": "^4.8.10", "jsonwebtoken": "^9.0.2", "zod": "^4.0.14"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/bun": "^1.2.19", "@types/jsonwebtoken": "^9.0.10", "prisma": "^6.13.0", "tsdown": "^0.12.9", "typescript": "^5.9.2"}}