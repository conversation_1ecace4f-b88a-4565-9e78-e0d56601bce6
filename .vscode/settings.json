{"editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[css]": {"editor.defaultFormatter": "biomejs.biome"}, "[graphql]": {"editor.defaultFormatter": "biomejs.biome"}, "typescript.tsdk": "node_modules/typescript/lib", "editor.formatOnSave": true, "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never", "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}}