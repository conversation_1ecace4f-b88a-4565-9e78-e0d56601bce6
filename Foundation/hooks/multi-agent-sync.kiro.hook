{"enabled": true, "name": "Multi-Agent Synchronization", "description": "Automatically synchronizes configuration files for Claude Code, Cursor, and Windsurf agents when steering documents in .kiro/steering/ are modified, ensuring all AI assistants maintain consistent project context and coding standards", "version": "1", "when": {"type": "fileEdited", "patterns": [".kiro/steering/*.md"]}, "then": {"type": "askAgent", "prompt": "The steering documents in .kiro/steering/ have been updated. Please analyze all .md files in this directory and synchronize the agent configurations:\n\n1. **Extract Core Information** from all steering documents:\n   - Project structure and technology stack\n   - Development guidelines and conventions  \n   - Code quality rules and accessibility standards\n   - Testing and deployment practices\n\n2. **Update Agent-Specific Configurations**:\n   - **Claude Code**: Update `.claude/CLAUDE.md` with comprehensive project context\n   - **Cursor**: Update `.cursor/rules` with concise development rules\n   - **Windsurf**: Update `.windsurfrules` with structured guidelines\n\n3. **Ensure Consistency**:\n   - Maintain identical core principles across all agents\n   - Adapt format and verbosity to each agent's standards\n   - Preserve agent-specific configuration patterns\n\nFocus on creating configurations that reflect the current state of the project and provide clear, actionable guidance for each AI assistant."}}